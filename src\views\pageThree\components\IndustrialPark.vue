<!--
  放款企业产业园区分布组件
  功能：
  1. 展示放款企业的产业园区分布情况
  2. 提供园区数据的柱状图可视化
  3. 支持数据自动滚动展示
  4. 提供详情弹窗查看完整数据
  5. 显示每个园区的放款金额和笔数
-->
<template>
  <div class="IndustrialPark-fkqyFour">
    <!-- 头部区域：标题和更多按钮 -->
    <div class="IndustrialPark-headerImg">
      <div>放款企业产业园区分布</div>
      <div class="ChainDistribution-rightClass">
        <div class="ChainDistribution-djgdClass" @click="handleClick">
          点击查看更多
        </div>
        <div>
          <img
            src="@/assets/dp/rightT.png"
            class="ChainDistribution-imgRight"
          />
        </div>
      </div>
    </div>
    <!-- 头部统计信息 -->
    <div class="IndustrialPark-industry-park-header">
      放款资金投放产业园区：<span class="IndustrialPark-industry-park-total"
        >52</span
      >
      个
    </div>
    <!-- 底部内容区域：展示园区数据 -->
    <div class="IndustrialPark-bottomClass">
<!--       <vue-seamless-scroll
      ref="vueSeamlessScroll1"
      style="height: 100%; overflow: hidden"
      :class-option="classOption"
      @mousewheel.native="handleScroll"
    > -->
        <div ref="parkChart" class="IndustrialPark-park-chart"></div>
<!--       </vue-seamless-scroll> -->
    </div>
<!--     <div v-if="dialogVisible" class="dialogBar-model" @click="handleDialogClose">
      <div class="dialogBar-content" @click.stop>
        <div class="dialogBar-header-box">
          <div class="dialogBar-header">
            <div class="dialogBar-titleBox1">
              <span class="dialogBar-title">放款企业产业园区分布</span>
            </div>
          </div>
        </div>
        <div class="dialogBar-body">
          <div class="dialogBar-chart" ref="dialogChart"     
            :style="{
              width: '100%',
              height: '100%',
              background: 'rgba(0, 24, 48, 0.1)'
            }"
          ></div>
        </div>
      </div>
    </div> -->
    <dialog-bar-park
  :visible="dialogVisible"
  :data="parkList"
  :title="'放款企业产业园区分布'"
  :color-stops="colorStops"
  :display-count="12"
  @close="handleDialogClose"
/>
  </div>
</template>

<script>
import { productUpdateList, queryRealtimeMetricList } from "@/api/article.js";
import * as echarts from "echarts";
import DialogBarPark from "../../components/dialogBar-park.vue";
export default {
  name: "szyyFour",
  components: {
    DialogBarPark,
  },
  data() {
    return {
      dialogVisible: false,
      classOption: {
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 50, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      },
      colorStops: [
        {
          offset: 0,
          color: "#16DEE1 ",
        },
        {
          offset: 1,
          color: "#034347",
        },
      ],
      visible: false,
      myChart: null,
      scrollTimer: null,
      currentIndex: 0,
      scrollSpeed: 3000, // 滚动间隔时间（毫秒）
      scrollStep: 1, // 每次滚动的条数
      flag: true,
      listData: [],
      rrObj: {
        qaNum: "",
        sessionNum: "",
        userAcccess: "",
        useNum: "",
      },
      RealtimeQuery: "",
      RealtimeAvg: "",
      bankNames: [
        "中国\n建设银行",
        "中国\n农业银行",
        "成都\n银行",
        "中国\n工商银行",
        "中国\n银行",
        "中国\n交通银行",
      ],
     
      backgroundData: [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10], // 百分比
      labelData: [
        { percent: "23%", amount: "130万" },
        { percent: "23%", amount: "130万" },
        { percent: "23%", amount: "130万" },
        { percent: "23%", amount: "130万" },
        { percent: "23%", amount: "130万" },
        { percent: "23%", amount: "130万" },
      ],
      parkList1: [
        { name: "温江国家农业科技园区", amount: "3.8", count: 4562 },
        { name: "国宾都市文旅产业园", amount: "2.9", count: 3289 },
        { name: "四川成都新津经济开发区", amount: "2.3", count: 2756 },
        { name: "荷花池商务商贸产业园", amount: "1.8", count: 2156 },
        { name: "四川成都成华经济开发区", amount: "1.5", count: 1892 },
        { name: "成都—阿坝工业园区", amount: "1.2", count: 1568 },
        { name: "四川天府新兴经济开发区（拟筹）", amount: "0.9", count: 1234 },
        { name: "华西转化医学产业园", amount: "0.8", count: 1123 },
        { name: "四川成都武侯经济开发区", amount: "0.7", count: 987 },
        { name: "四川成都锦江经济开发区", amount: "0.6", count: 856 },
        { name: "成都国际铁路港经济技术开发区", amount: "0.5", count: 765 },
        { name: "四川金堂经济开发区", amount: "0.4", count: 654 },
        { name: "天府果荟国家现代农业产业园", amount: "0.3", count: 543 },
        { name: "成都音乐文创园", amount: "0.2", count: 432 },
        { name: "四川邛崃经济开发区", amount: "0.1", count: 321 },
        { name: "四川成都郫都高新技术产业园区", amount: "3.8", count: 4562 },
        { name: "四川彭州经济开发区", amount: "2.9", count: 3289 },
        { name: "城北（香城）新消费活力区", amount: "2.3", count: 2756 },
        { name: "四川天府国际空港经济开发区（在筹）", amount: "1.8", count: 2156 },
        { name: "成都高新技术产业开发区", amount: "1.5", count: 1892 },
        { name: "成都新材料产业化工园区", amount: "1.2", count: 1568 },
        { name: "天府中央商务区", amount: "0.9", count: 1234 },
        { name: "成都经济技术开发区", amount: "0.8", count: 1123 },
        { name: "四川成都温江高新技术产业园区", amount: "0.7", count: 987 },
        { name: "四川都江堰经济开发区", amount: "0.6", count: 856 },
        { name: "成都熊猫国际旅游度假区", amount: "0.5", count: 765 },
        { name: "四川成都新都高新技术产业园区", amount: "0.4", count: 654 },
        { name: "少城国际文创谷", amount: "0.3", count: 543 },
        { name: "四川崇州经济开发区", amount: "0.2", count: 432 },
        { name: "简阳临空经济产业园", amount: "0.1", count: 321 },
        { name: "四川成都金牛高新技术产业园区", amount: "3.8", count: 4562 },
        { name: "天府数字农旅产业园", amount: "2.9", count: 3289 },
        { name: "四川成都双流经济开发区", amount: "2.3", count: 2756 },
        { name: "都江堰现代文旅融合园区", amount: "1.8", count: 2156 },
        { name: "四川成都青羊经济开发区", amount: "1.5", count: 1892 },
        { name: "东郊记忆艺术区", amount: "1.2", count: 1568 },
        { name: "成都影视城", amount: "0.9", count: 1234 },
        { name: "街子古镇群旅游度假区", amount: "0.8", count: 1123 },
        { name: "四川简阳经济开发区", amount: "0.7", count: 987 },
        { name: "四川蒲江经济开发区", amount: "0.6", count: 856 },
        { name: "四川大邑经济开发区", amount: "0.5", count: 765 },
        { name: "龙门山旅游度假区", amount: "0.4", count: 654 },
        { name: "天府蔬香农业产业园", amount: "0.3", count: 543 },
        { name: "中国天府农业博览园", amount: "0.2", count: 432 },
        { name: "天府现代种业园", amount: "0.1", count: 321 },
        { name: "西岭冰雪·安仁文博国际文化旅游区", amount: "0.1", count: 321 },
        { name: "天府菌都农业产业园", amount: "0.1", count: 321 },
        { name: "天府粮仓国家现代农业产业园", amount: "0.1", count: 321 },
        { name: "天府奥体公园", amount: "0.1", count: 321 },
      ],
      parkList: [],
      dialogChart: null,
      dialogAutoPlayTimer: null,
      dialogCurrentIndex: 0,
      dialogDisplayCount: 12, // 修改为15条数据
    };
  },
  mounted() {
    // 直接使用 localStorage 中的数据
    this.parkList = JSON.parse(localStorage.getItem("parkList")) || [];

    console.log(this.parkList,'parkList');
    
    // 初始化图表
    this.initBar();
  },
  methods: {
    handleScroll(e) {
      this.$refs.vueSeamlessScroll1.xPos =
        this.$refs.vueSeamlessScroll1.xPos - e.deltaX + 60;
      // 如果是正数 说明是往上滚
      if (this.$refs.vueSeamlessScroll1.xPos > 0) {
        this.$refs.vueSeamlessScroll1.xPos = 0;
        return;
      }
    },
    handleClose() {
      this.visible = false;
    },
    handleClick() {
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.initDialogChart();
      });
    },
    handleDialogClose() {
      this.dialogVisible = false;
      if (this.dialogAutoPlayTimer) {
        clearInterval(this.dialogAutoPlayTimer);
        this.dialogAutoPlayTimer = null;
      }
      if (this.dialogChart) {
        this.dialogChart.dispose();
        this.dialogChart = null;
      }
      this.dialogCurrentIndex = 0;
    },
    initBar() {
      if (!this.$refs.parkChart) return;
      this.myChart = echarts.init(this.$refs.parkChart);
      // 确保数据按金额从大到小排序
      const sortedData = [...this.parkList].sort((a, b) => parseFloat(b.amount) - parseFloat(a.amount));
      const option = {
        // 动画配置
        animation: true,
        animationDuration: 1000,
        animationEasing: 'cubicOut',
        animationDelay: 0,
        grid: {
          left: this.$autoFontSize(0),
          right: this.$autoFontSize(0),
          bottom: this.$autoFontSize(6),
          top: this.$autoFontSize(40),
          containLabel: true,
        },
        dataZoom: [
          {
            type: "slider",
            show: false,
            xAxisIndex: [0],
            height: 8,
            bottom: 2,
            borderColor: "transparent",
            backgroundColor: "#0a2e4a",
            fillerColor: "rgba(43,156,255,0.2)",
            handleStyle: {
              color: "#2b9cff",
              borderColor: "#2b9cff",
            },
            moveHandleStyle: {
              color: "#2b9cff",
            },
            selectedDataBackground: {
              lineStyle: {
                color: "#2b9cff",
              },
              areaStyle: {
                color: "#2b9cff",
              },
            },
            emphasis: {
              handleStyle: {
                color: "#398fff",
              },
            },
            showDetail: false,
            showDataShadow: false,
            brushSelect: false,
            zoomLock: false,
            throttle: 100,
            z: 100,
          },
        ],
        tooltip: {
          show: false,
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: function (params) {
            const data = params[0];
            return `${data.name}<br/>
                    <span style="color:#ffce7c">${data.value}亿</span><br/>
                    <span style="color:#23eaff">${data.count}笔</span>`;
          },
        },
        xAxis: {
          type: "category",
          data: sortedData.map((item) => item.name),
          axisLabel: {
            color: "#fff",
            fontSize: this.$autoFontSize(13),
            interval: 0,
            width: 500,
            overflow: "break",
            formatter: function (value) {
              return value.replace(/(.{6})/g, "$1\n");
            },
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
            },
          },
        },
        yAxis: {
          show: false,
          type: "value",
          name: "金额（亿）",
          nameTextStyle: {
            color: "#fff",
            fontSize: this.$autoFontSize(12),
          },

          axisLabel: {
            color: "#fff",
            fontSize: this.$autoFontSize(12),
          },
          splitLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.1)",
            },
          },
        },
        series: [
          {
            name: "全量背景图",
            type: "bar",
            barGap: "-100%",
            data: [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10],
            barWidth: this.$autoFontSize(12),
            itemStyle: {
              normal: {
                color: "rgba(63, 169, 245, 0.2)",
              },
            },
            z: 0,
          },
          {
            data: sortedData.map((item) => ({
              value: parseFloat(item.amount),
              count: item.count
            })),
            type: "bar",
            barWidth: this.$autoFontSize(12),
            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#16DEE1 ",
                  },
                  {
                    offset: 1,
                    color: "#034347",
                  },
                ],
              },
              borderRadius: [0, 0, 0, 0],
            },
            label: {
              show: true,
              position: "top",
              formatter: (params) => {
                console.log(params,'params');
                const count = this.countMap ? this.countMap[params.name] : 0;
                return `{amount|${params.value}亿}\n{count|${params.data.count}笔}`;
              },
              rich: {
                amount: {
                  color: "#ffce7c",
                  textAlign: "center",
                  fontSize: this.$autoFontSize(14),
                  padding: [0, 0, this.$autoFontSize(5), 0],
                  align: 'center',
                  verticalAlign: 'middle'
                },
                count: {
                  color: "#23eaff",
                  textAlign: "center",
                  fontSize: this.$autoFontSize(14),
                  align: 'center',
                  verticalAlign: 'middle'
                },
              },
            },
          },
          {
            type: "scatter",
            coordinateSystem: "cartesian2d",
            symbol: "circle",
            symbolSize: [this.$autoFontSize(12), this.$autoFontSize(6)],
            itemStyle: {
              color: "#fff",
              shadowColor: "#fff",
            },
            z: 3,
            data: sortedData.map((item, idx) => [
              idx,
              parseFloat(item.amount),
            ]),
          },
        ],
      };
      this.myChart.setOption(option);

      this.startAutoScroll(sortedData.length);       
      window.addEventListener("resize", () => {
        this.myChart.resize();
      });
    },
    startAutoScroll(totalItems) {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
      }

      // 确保数据按金额从大到小排序
      const sortedData = [...this.parkList].sort((a, b) => parseFloat(b.amount) - parseFloat(a.amount));

      this.scrollTimer = setInterval(() => {
        this.performCarouselAnimation(sortedData);
      }, this.scrollSpeed);
    },

    // 执行轮播动画
    performCarouselAnimation(sortedData) {
      // 获取当前图表中显示的数据
      const option = this.myChart.getOption();
      const currentData = option.xAxis[0].data;

      // 第一步：让第一个柱子"飞出"（降低高度）
      const flyOutData = [...currentData].map((name, index) => {
        const item = sortedData.find((d) => d.name === name);
        if (index === 0) {
          // 第一个柱子高度降为0
          return { value: 0, count: 0 };
        } else {
          // 其他柱子保持原值
          return item ? {
            value: parseFloat(item.amount),
            count: item.count
          } : { value: 0, count: 0 };
        }
      });

      const flyOutScatterData = [...currentData].map((name, idx) => {
        if (idx === 0) {
          return [idx, 0]; // 第一个散点也降为0
        } else {
          const item = sortedData.find((d) => d.name === name);
          return [idx, item ? parseFloat(item.amount) : 0];
        }
      });

      // 执行"飞出"动画
      this.myChart.setOption({
        animation: true,
        animationDuration: 500,
        animationEasing: 'cubicIn',
        series: [
          {
            name: "全量背景图",
            data: [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10],
          },
          {
            data: flyOutData,
            label: {
              show: true,
              position: "top",
              formatter: function (params) {
                return params.value > 0 ? `{amount|${params.value}亿}\n{count|${params.data.count}笔}` : '';
              },
              rich: {
                amount: {
                  color: "#ffce7c",
                  fontSize: this.$autoFontSize(14),
                  padding: [0, 0, 5, 0],
                },
                count: {
                  color: "#23eaff",
                  fontSize: this.$autoFontSize(14),
                },
              },
            },
          },
          {
            data: flyOutScatterData,
          },
        ],
      });

      // 第二步：600ms后重新排列数据并"飞入"新柱子
      setTimeout(() => {
        // 重新排序数据
        const newData = [...currentData];
        for (let i = 0; i < this.scrollStep; i++) {
          const item = newData.shift();
          newData.push(item);
        }

        // 根据新的顺序重新组织数据
        const newSeriesData = newData.map((name) => {
          const item = sortedData.find((d) => d.name === name);
          return item ? {
            value: parseFloat(item.amount),
            count: item.count
          } : { value: 0, count: 0 };
        });

        const newScatterData = newData.map((name, idx) => {
          const item = sortedData.find((d) => d.name === name);
          return [idx, item ? parseFloat(item.amount) : 0];
        });

        // 执行"飞入"动画
        this.myChart.setOption({
          animation: true,
          animationDuration: 800,
          animationEasing: 'elasticOut',
          animationDelay: function (idx) {
            // 最后一个柱子（新进入的）有额外延迟
            return idx === newData.length - 1 ? 200 : idx * 50;
          },
          xAxis: {
            data: newData,
          },
          series: [
            {
              name: "全量背景图",
              data: [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10],
            },
            {
              data: newSeriesData,
              label: {
                show: true,
                position: "top",
                formatter: function (params) {
                  return `{amount|${params.value}亿}\n{count|${params.data.count}笔}`;
                },
                rich: {
                  amount: {
                    color: "#ffce7c",
                    fontSize: this.$autoFontSize(14),
                    padding: [0, 0, 5, 0],
                  },
                  count: {
                    color: "#23eaff",
                    fontSize: this.$autoFontSize(14),
                  },
                },
              },
            },
            {
              data: newScatterData,
            },
          ],
        });
      }, 600);
    },
    initDialogChart() {
      if (!this.$refs.dialogChart) return;
      
      const container = this.$refs.dialogChart;
      if (container.offsetWidth === 0 || container.offsetHeight === 0) {
        console.warn('Chart container has no size, retrying...');
        setTimeout(() => this.initDialogChart(), 100);
        return;
      }

      if (this.dialogChart) {
        this.dialogChart.dispose();
      }

      this.dialogChart = echarts.init(container);
      this.updateDialogChart();
      window.addEventListener("resize", this.handleDialogResize);
    },
    handleDialogResize() {
      this.dialogChart && this.dialogChart.resize();
    },
    updateDialogChart() {
      if (!this.dialogChart) return;
      // 确保数据按金额从大到小排序
      const data = [...this.parkList].sort((a, b) => parseFloat(b.amount) - parseFloat(a.amount));
      const initialData = data.slice(0, this.dialogDisplayCount);
      
      this.dialogChart.setOption({
        grid: {
          left: this.$autoFontSize(0),
          right: this.$autoFontSize(0),
          bottom: this.$autoFontSize(6),
          top: this.$autoFontSize(20),
          containLabel: true
        },
        tooltip: {
          show: false,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const data = params[0];
            return `${data.name}<br/>
                    <span style="color:#ffce7c">${data.value}亿</span><br/>
                    <span style="color:#23eaff">${data.data.count}笔</span>`;
          }
        },
        xAxis: {
          type: 'category',
          data: initialData.map(item => item.name),
          axisLabel: {
            color: '#fff',
            fontSize: this.$autoFontSize(13),
            interval: 0,
            width: 300, // 减小宽度以适应更多数据
            overflow: 'break',
            formatter: function(value) {
              return value.replace(/(.{6})/g, '$1\n');
            },
            margin: 20 // 减小间距以适应更多数据
          },
          axisTick: {
            show: false  // 隐藏刻度线
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.2)'
            }
          }
        },
        yAxis: {
          show: false,
          type: 'value',
          name: '金额（亿）',
          nameTextStyle: {
            color: '#fff',
            fontSize: this.$autoFontSize(12)
          },
          axisLabel: {
            color: '#fff',
            fontSize: this.$autoFontSize(12)
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)'
            }
          }
        },
        series: [
          {
            name: "全量背景图",
            type: "bar",
            barGap: "-100%",
            data: this.backgroundData,
            barWidth: this.$autoFontSize(12),
            itemStyle: {
              normal: {
                color: "rgba(63, 169, 245, 0.2)",
              },
            },
            z: 0,
          },
          {
            data: initialData.map(item => ({
              value: parseFloat(item.amount),
              count: item.count
            })),
            type: 'bar',
            barWidth: this.$autoFontSize(12),
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: this.colorStops
              },
              borderRadius: [0, 0, 0, 0]
            },
            label: {
              show: true,
              position: 'top',
              formatter: function(params) {
                return `{amount|${params.value}亿}\n{count|${params.data.count}笔}`;
              },
              rich: {
                amount: {
                  color: '#ffce7c',
                  fontSize: this.$autoFontSize(14),
                  textAlign: 'center',
                  align: 'center',
                  verticalAlign: 'middle'
                },
                count: {
                  color: '#23eaff',
                  fontSize: this.$autoFontSize(14),
                  textAlign: 'center',
                  align: 'center',
                  verticalAlign: 'middle'
                }
              }
            }
          },
          {
            type: "scatter",
            coordinateSystem: "cartesian2d",
            symbol: "circle",
            symbolSize: [this.$autoFontSize(12), this.$autoFontSize(6)],
            itemStyle: {
              color: "#fff",
              shadowColor: "#fff",
            },
            z: 3,
            data: initialData.map((item, idx) => [
              idx,
              parseFloat(item.amount),
            ]),
          }
        ]
      });

      this.startDialogAutoPlay();
    },
    startDialogAutoPlay() {
      if (this.dialogAutoPlayTimer) {
        clearInterval(this.dialogAutoPlayTimer);
      }

      // 确保数据按金额从大到小排序
      const sortedData = [...this.parkList].sort((a, b) => parseFloat(b.amount) - parseFloat(a.amount));

      this.dialogAutoPlayTimer = setInterval(() => {
        const totalItems = sortedData.length;
        const start = this.dialogCurrentIndex;
        const end = Math.min(start + this.dialogDisplayCount, totalItems);

        // 如果到达末尾，重新开始
        if (start >= totalItems) {
          this.dialogCurrentIndex = 0;
          return;
        }

        this.dialogChart.setOption({
          xAxis: {
            data: sortedData.slice(start, end).map(item => item.name),
            axisTick: {
            show: false  // 隐藏刻度线
          },
            axisLabel: {
              width: 300,
              margin: 20
            }
          },
          series: [
            {
              name: "全量背景图",
              data: this.backgroundData,
            },
            {
              data: sortedData.slice(start, end).map(item => ({
                value: parseFloat(item.amount),
                count: item.count
              })),
              label: {
                show: true,
                position: 'top',
                formatter: function(params) {
                  return `{amount|${params.value}亿}\n{count|${params.data.count}笔}`;
                },
                rich: {
                  amount: {
                    color: '#ffce7c',
                    fontSize: this.$autoFontSize(14),
                    textAlign: 'center',
                    align: 'center',
                    verticalAlign: 'middle'
                  },
                  count: {
                    color: '#23eaff',
                    fontSize: this.$autoFontSize(14),
                    textAlign: 'center',
                    align: 'center',
                    verticalAlign: 'middle'
                  }
                }
              }
            },
            {
              data: sortedData.slice(start, end).map((item, idx) => [
                idx,
                parseFloat(item.amount),
              ]),
            }
          ]
        });

        this.dialogCurrentIndex += 1; // 每次滚动一条数据
      }, 3000); // 每3秒滚动一次
    },
  },
  beforeDestroy() {
    if (this.scrollTimer) {
      clearInterval(this.scrollTimer);
    }
    if (this.myChart) {
      this.myChart.dispose();
    }
    window.removeEventListener("resize", this.handleDialogResize);
  },
};
</script>
<style scoped lang="scss">
.IndustrialPark-fkqyFour {
  width: 940px;
  height: 27vh;
  display: flex;
  flex-direction: column;

  .IndustrialPark-headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
  }
  .ChainDistribution-rightClass {
    display: flex;
    align-items: center;
    justify-content: center;

    .ChainDistribution-djgdClass {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 26px;
      color: #77c1ff;
      letter-spacing: 1px;
      cursor: pointer;
      margin-right: 12px;
    }

    .ChainDistribution-imgRight {
      width: 12px;
      height: 22px;
      position: relative;
      top: -1px;
    }
  }
  .IndustrialPark-bottomClass {
    width: 100%;
    flex: 1;
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 2px solid;
    border-top: 0;
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    position: relative;
    overflow: hidden;
    min-height: 300px;
  }
}

.IndustrialPark-industry-park-header {
  width: 95%;
  padding-left: 5%;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28px;
  color: #ffffff;
  letter-spacing: 1px;
  text-align: left;
  font-style: normal;
}

.IndustrialPark-industry-park-total {
  font-family: OPPOSans, OPPOSans;
  font-weight: bold;
  font-size: 44px;
  color: #ffffff;
  line-height: 70px;
  letter-spacing: 2px;
  text-align: center;
  font-style: normal;
  background: linear-gradient(90deg, #ffffff 0%, #7cebff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.IndustrialPark-park-chart {
  width: 900%;
  height: 100%;
}

.dialogBar-model {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dialogBar-content {
  background: url("~@/assets/dp/dialogMax.png");
  background-size: 100% 100%;
  border-radius: 12px;
  width: 60%;
  height: 60%;
  box-shadow: 0 0 30px #0a1a2a;
  position: relative;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.dialogBar-header-box {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.dialogBar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 24px;
  font-size: 20px;
  color: #fff;
  font-weight: bold;
  position: relative;
}

.dialogBar-titleBox1 {
  width: 940px;
  background-image: url("~@/assets/dp/headerbg.png");
  background-size: 940px 89px;
  background-repeat: no-repeat;
  height: 89px;
  display: flex;
  justify-content:flex-start;
  align-items: center;
  padding-left: 150px;
  .dialogBar-title {
    font-family: YouSheBiaoTiHei;
    font-weight: 200;
    font-size: 42px;
    color: #ffffff;
    line-height: 89px;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    text-align: left;
    font-style: normal;
    margin-left: -40px;
    width: 100%;
  }
}

.dialogBar-body {
  flex: 1;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.dialogBar-chart {
  width: 100%;
  height: 100%;
  background: rgba(0, 24, 48, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
